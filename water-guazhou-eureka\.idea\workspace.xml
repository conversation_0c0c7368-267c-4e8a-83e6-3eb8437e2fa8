<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="cdc19203-3826-4286-945c-166f77417049" name="Changes" comment="格式化">
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/ScadaController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/SupplierController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/SupplierGoodsController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/gis/GISController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/gis/GisExceptionUploadController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/gis/GisLabelController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/gis/GisPipeAdditionalInfoController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/gis/GisPlanController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/gis/Tb3DDataController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/smartOperation/construction/project/SoBiddingCompanyController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/smartOperation/construction/project/SoBiddingController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/smartOperation/construction/project/SoProjectAcceptController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/smartOperation/construction/project/SoProjectArchiveController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/smartOperation/construction/project/SoProjectController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/smartOperation/construction/project/SoProjectOperateRecordController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/smartOperation/construction/project/SoProjectSettlementController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/smartProduction/dispatch/DispatchMethodController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/smartProduction/dispatch/EmergencyPlanController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/smartProduction/dispatch/EmergencyUserController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/smartProduction/dispatch/EmergencyVehicleController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/smartProduction/dispatch/ExpertInfoController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/smartProduction/dispatch/OrderRecordController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/smartProduction/dispatch/OrderRecordTypeController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/video/VideoConfigController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/controller/video/VideoController.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$USER_HOME$/Desktop/thingsboard-iot-2.3.0-boot.jar!/BOOT-INF/classes/org/thingsboard/server/service/install/CassandraTsDatabaseSchemaService.class" root0="SKIP_INSPECTION" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\apache-maven-3.6.3" />
        <option name="localRepository" value="D:\apache-maven-3.6.3\.m2" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\apache-maven-3.6.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2vWHwaOVfM42bdlyPLpOUDZEnd4" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.IstarZuulApplication.executor&quot;: &quot;Run&quot;,
    &quot;Maven.istar_zuul [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.istar_zuul [install].executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-eureka\src\main\java\com\istar_zuul\istar_zuul" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="Application" />
      </set>
    </option>
  </component>
  <component name="RunManager">
    <configuration name="IstarZuulApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.istar_zuul.istar_zuul.IstarZuulApplication" />
      <module name="istar_zuul" />
      <option name="VM_PARAMETERS" value="-Dspring.output.ansi.enabled=ALWAYS" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.istar_zuul.istar_zuul.test.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.IstarZuulApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="cdc19203-3826-4286-945c-166f77417049" name="Changes" comment="" />
      <created>1744254149449</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1744254149449</updated>
    </task>
    <task id="LOCAL-00001" summary="格式化">
      <option name="closed" value="true" />
      <created>1744265269893</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1744265269893</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="格式化" />
    <option name="LAST_COMMIT_MESSAGE" value="格式化" />
  </component>
</project>