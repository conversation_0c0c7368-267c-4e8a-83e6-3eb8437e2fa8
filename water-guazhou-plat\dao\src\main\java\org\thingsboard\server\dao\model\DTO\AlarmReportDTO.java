package org.thingsboard.server.dao.model.DTO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.thingsboard.server.dao.model.ModelConstants;

import javax.persistence.Column;
import java.util.Date;

/**
 * 报警报表轻量级DTO - 只包含前端需要的字段，减少数据传输量
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlarmReportDTO {
    
    /**
     * 报警ID
     */
    private String id;
    
    /**
     * 站点ID
     */
    private String stationId;
    
    /**
     * 报警标题
     */
    private String title;
    
    /**
     * 报警时间
     */
    private Date time;

    /**
     * 报警信息
     */
    private String alarmInfo;

    /**
     * 报警级别
     */
    private String alarmLevel;
    
    /**
     * 报警状态
     */
    private String alarmStatus;
    
    /**
     * 站点名称
     */
    private String stationName;
    
    /**
     * 站点类型
     */
    private String stationType;
}
