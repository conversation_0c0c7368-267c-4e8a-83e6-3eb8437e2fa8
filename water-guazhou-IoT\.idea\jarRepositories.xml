<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="central" />
      <option name="url" value="http://repo1.maven.org/maven2/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="typesafe" />
      <option name="name" value="Typesafe Repository" />
      <option name="url" value="http://repo.typesafe.com/typesafe/releases/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="http://city189.cn:1002/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="spring-milestones" />
      <option name="name" value="Spring Milestones" />
      <option name="url" value="http://city189.cn:1002/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="spring-snapshots" />
      <option name="name" value="Spring Snapshots" />
      <option name="url" value="http://city189.cn:1002/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="nexus-releases" />
      <option name="name" value="ct-group" />
      <option name="url" value="http://city189.cn:1002/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jenkins" />
      <option name="name" value="Jenkins Repository" />
      <option name="url" value="http://city189.cn:1002/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="spring-milestones" />
      <option name="name" value="Spring Milestones" />
      <option name="url" value="https://repo.spring.io/milestone" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="spring-snapshots" />
      <option name="name" value="Spring Snapshots" />
      <option name="url" value="https://repo.spring.io/snapshot" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="sonatype" />
      <option name="name" value="sonatype" />
      <option name="url" value="https://oss.sonatype.org/content/groups/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jenkins" />
      <option name="name" value="Jenkins Repository" />
      <option name="url" value="http://repo.jenkins-ci.org/releases" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="typesafe" />
      <option name="name" value="Typesafe Repository" />
      <option name="url" value="http://city189.cn:1002/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="sonatype" />
      <option name="name" value="sonatype" />
      <option name="url" value="http://city189.cn:1002/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="central" />
      <option name="url" value="http://city189.cn:1002/repository/maven-public/" />
    </remote-repository>
  </component>
</project>