<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="441eff54-dbd9-4582-b881-c281efea59aa" name="Changes" comment="修改dockerfile路径" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\apache-maven-3.6.3" />
        <option name="localRepository" value="D:\apache-maven-3.6.3\.m2" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\apache-maven-3.6.3\conf\settings.xml" />
        <option name="workOffline" value="true" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2zf6lkcJYjnk6c4oz7EtoNSCJpV" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.ThingsboardServerApplication.executor": "Run",
    "Maven.thingsboard [clean].executor": "Run",
    "Maven.thingsboard [compile].executor": "Run",
    "Maven.thingsboard [install].executor": "Run",
    "Maven.thingsboard [package].executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/Code-Yanfayun/water/guazhou/water-guazhou-IoT",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "settings.editor.selected.configurable": "editor.preferences.import"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-IoT" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-IoT\docker" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="Application" />
      </set>
    </option>
  </component>
  <component name="RunManager">
    <configuration name="ThingsboardServerApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="org.thingsboard.server.ThingsboardServerApplication" />
      <module name="application" />
      <option name="VM_PARAMETERS" value="-Dspring.output.ansi.enabled=ALWAYS" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.thingsboard.server.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="water-guazhou-IoT" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="water-guazhou-IoT" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.ThingsboardServerApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="441eff54-dbd9-4582-b881-c281efea59aa" name="Changes" comment="" />
      <created>1752109933995</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752109933995</updated>
    </task>
    <task id="LOCAL-00001" summary="修改gradle配置">
      <option name="closed" value="true" />
      <created>1752128005754</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752128005754</updated>
    </task>
    <task id="LOCAL-00002" summary="修改gradle配置，研发云">
      <option name="closed" value="true" />
      <created>1752128919587</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752128919587</updated>
    </task>
    <task id="LOCAL-00003" summary="研发云修改gradle配置">
      <option name="closed" value="true" />
      <created>1752129339796</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752129339796</updated>
    </task>
    <task id="LOCAL-00004" summary="研发云修改gradle配置">
      <option name="closed" value="true" />
      <created>1752130233613</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1752130233613</updated>
    </task>
    <task id="LOCAL-00005" summary="研发云重新增加gradle配置">
      <option name="closed" value="true" />
      <created>1752133179500</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1752133179500</updated>
    </task>
    <task id="LOCAL-00006" summary="研发云重新增加gradle配置">
      <option name="closed" value="true" />
      <created>1752133496544</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1752133496544</updated>
    </task>
    <task id="LOCAL-00007" summary="研发云pom中修改gradle配置">
      <option name="closed" value="true" />
      <created>1752141408592</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1752141408592</updated>
    </task>
    <task id="LOCAL-00008" summary="本地gradle文件编译">
      <option name="closed" value="true" />
      <created>1752222260749</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1752222260749</updated>
    </task>
    <task id="LOCAL-00009" summary="升级gradle到3.5.1">
      <option name="closed" value="true" />
      <created>1752457830076</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1752457830076</updated>
    </task>
    <task id="LOCAL-00010" summary="注释公共库依赖源">
      <option name="closed" value="true" />
      <created>1752461746872</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1752461746872</updated>
    </task>
    <task id="LOCAL-00011" summary="修改dockerFile">
      <option name="closed" value="true" />
      <created>1752462537168</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1752462537168</updated>
    </task>
    <task id="LOCAL-00012" summary="修改pom文件，排除gradle相关依赖和打包">
      <option name="closed" value="true" />
      <created>1753858772561</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1753858772561</updated>
    </task>
    <task id="LOCAL-00013" summary="修改dockerfile">
      <option name="closed" value="true" />
      <created>1753859191519</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1753859191519</updated>
    </task>
    <task id="LOCAL-00014" summary="修改dockerfile拼写问题">
      <option name="closed" value="true" />
      <created>1753859556805</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1753859556805</updated>
    </task>
    <task id="LOCAL-00015" summary="修改dockerfile拼写问题">
      <option name="closed" value="true" />
      <created>1753859751141</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1753859751141</updated>
    </task>
    <task id="LOCAL-00016" summary="修改dockerfile路径">
      <option name="closed" value="true" />
      <created>1753860078443</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1753860078443</updated>
    </task>
    <task id="LOCAL-00017" summary="修改dockerfile路径">
      <option name="closed" value="true" />
      <created>1753860256507</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1753860256507</updated>
    </task>
    <task id="LOCAL-00018" summary="修改dockerfile路径">
      <option name="closed" value="true" />
      <created>1753861025600</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1753861025600</updated>
    </task>
    <task id="LOCAL-00019" summary="修改dockerfile路径">
      <option name="closed" value="true" />
      <created>1753861291930</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1753861291930</updated>
    </task>
    <task id="LOCAL-00020" summary="修改dockerfile路径">
      <option name="closed" value="true" />
      <created>1753862083340</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1753862083340</updated>
    </task>
    <task id="LOCAL-00021" summary="修改dockerfile路径">
      <option name="closed" value="true" />
      <created>1753862336990</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1753862336990</updated>
    </task>
    <task id="LOCAL-00022" summary="修改dockerfile路径">
      <option name="closed" value="true" />
      <created>1753862551123</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1753862551123</updated>
    </task>
    <option name="localTasksCounter" value="23" />
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="修改gradle配置" />
    <MESSAGE value="修改gradle配置，研发云" />
    <MESSAGE value="研发云修改gradle配置" />
    <MESSAGE value="研发云重新增加gradle配置" />
    <MESSAGE value="研发云pom中修改gradle配置" />
    <MESSAGE value="本地gradle文件编译" />
    <MESSAGE value="升级gradle到3.5.1" />
    <MESSAGE value="注释公共库依赖源" />
    <MESSAGE value="修改dockerFile" />
    <MESSAGE value="修改pom文件，排除gradle相关依赖和打包" />
    <MESSAGE value="修改dockerfile" />
    <MESSAGE value="修改dockerfile拼写问题" />
    <MESSAGE value="修改dockerfile路径" />
    <option name="LAST_COMMIT_MESSAGE" value="修改dockerfile路径" />
  </component>
</project>